/**
 * 测试配置相关 API 的脚本
 * 验证 electronApi 中的配置方法是否正常工作
 */

const fetch = require('node-fetch')

// 测试配置
const BASE_URL = 'http://localhost:3030'

// 测试函数
async function testConfigApi() {
  console.log('🔍 开始测试配置 API...\n')
  
  const tests = [
    {
      name: '获取配置',
      method: 'GET',
      url: '/api/config/get'
    },
    {
      name: '获取本地配置',
      method: 'GET', 
      url: '/api/config/local'
    },
    {
      name: '获取本地配置（指定版本）',
      method: 'GET',
      url: '/api/config/local?version=1.0.0'
    },
    {
      name: '获取可用版本列表',
      method: 'GET',
      url: '/api/config/versions'
    },
    {
      name: '获取配置下载进度',
      method: 'GET',
      url: '/api/config/progress'
    }
  ]
  
  let passedTests = 0
  let totalTests = tests.length
  
  for (const test of tests) {
    try {
      console.log(`📋 测试: ${test.name}`)
      console.log(`   请求: ${test.method} ${test.url}`)
      
      const response = await fetch(`${BASE_URL}${test.url}`, {
        method: test.method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: test.body ? JSON.stringify(test.body) : undefined
      })
      
      const data = await response.json()
      
      if (response.ok) {
        console.log(`   ✅ 成功: ${response.status}`)
        console.log(`   响应: ${JSON.stringify(data, null, 2).substring(0, 200)}...`)
        passedTests++
      } else {
        console.log(`   ❌ 失败: ${response.status}`)
        console.log(`   错误: ${JSON.stringify(data, null, 2)}`)
      }
    } catch (error) {
      console.log(`   ❌ 异常: ${error.message}`)
    }
    
    console.log('')
  }
  
  console.log('📊 测试结果:')
  console.log(`- 通过: ${passedTests}/${totalTests}`)
  console.log(`- 成功率: ${Math.round(passedTests / totalTests * 100)}%`)
  
  if (passedTests === totalTests) {
    console.log('🎉 所有配置 API 测试通过！')
    return true
  } else {
    console.log('⚠️  部分配置 API 测试失败')
    return false
  }
}

// 测试浏览器环境下的 electronApi 配置方法
async function testElectronApiConfig() {
  console.log('\n🔍 测试 electronApi 配置方法...\n')
  
  // 模拟浏览器环境
  global.window = {
    electron: undefined,
    api: undefined
  }
  
  try {
    // 动态导入 electronApi
    const electronApiModule = await import('../src/renderer/src/utils/electronApi.js')
    const electronApi = electronApiModule.default
    
    console.log('📋 测试 electronApi.config.getConfig()')
    const config = await electronApi.config.getConfig()
    console.log('   结果:', config ? '✅ 成功' : '❌ 失败')
    
    console.log('📋 测试 electronApi.config.getLocalConfig()')
    const localConfig = await electronApi.config.getLocalConfig()
    console.log('   结果:', localConfig ? '✅ 成功' : '❌ 失败')
    
    console.log('📋 测试 electronApi.config.getAvailableVersions()')
    const versions = await electronApi.config.getAvailableVersions()
    console.log('   结果:', versions ? '✅ 成功' : '❌ 失败')
    
    console.log('📋 测试 electronApi.config.getDownloadProgress()')
    const progress = await electronApi.config.getDownloadProgress()
    console.log('   结果:', progress ? '✅ 成功' : '❌ 失败')
    
    console.log('\n🎉 electronApi 配置方法测试完成！')
    return true
  } catch (error) {
    console.error('❌ electronApi 测试失败:', error.message)
    return false
  }
}

// 主函数
async function main() {
  console.log('🚀 配置 API 测试工具\n')
  
  // 检查服务是否运行
  try {
    const response = await fetch(`${BASE_URL}/health`)
    if (!response.ok) {
      throw new Error('服务未响应')
    }
    console.log('✅ 主进程 HTTP 服务正在运行\n')
  } catch (error) {
    console.error('❌ 主进程 HTTP 服务未运行，请先启动 Electron 应用')
    console.error('   运行命令: npm run dev\n')
    process.exit(1)
  }
  
  // 运行测试
  const apiTestResult = await testConfigApi()
  const electronApiTestResult = await testElectronApiConfig()
  
  const allTestsPassed = apiTestResult && electronApiTestResult
  
  console.log('\n' + '='.repeat(50))
  console.log('📋 总体测试结果:')
  console.log(`- HTTP API 测试: ${apiTestResult ? '✅ 通过' : '❌ 失败'}`)
  console.log(`- electronApi 测试: ${electronApiTestResult ? '✅ 通过' : '❌ 失败'}`)
  console.log(`- 整体状态: ${allTestsPassed ? '🎉 全部通过' : '⚠️  存在问题'}`)
  
  process.exit(allTestsPassed ? 0 : 1)
}

// 运行测试
if (require.main === module) {
  main().catch(error => {
    console.error('测试运行失败:', error)
    process.exit(1)
  })
}

module.exports = { testConfigApi, testElectronApiConfig }
