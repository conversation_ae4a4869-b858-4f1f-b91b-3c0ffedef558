<template>
  <div class="electron-api-example">
    <h2>Electron API 统一入口示例</h2>
    
    <div class="section">
      <h3>环境信息</h3>
      <p>当前环境: {{ isElectron ? 'Electron' : '浏览器' }}</p>
      <p>测试模式: {{ isTest }}</p>
    </div>

    <div class="section">
      <h3>应用控制</h3>
      <button @click="closeApp">关闭应用</button>
      <button @click="restartApp">重启应用</button>
    </div>

    <div class="section">
      <h3>配置管理</h3>
      <button @click="getConfig">获取配置</button>
      <button @click="getMachineInfo">获取机器信息</button>
      <pre v-if="config">{{ JSON.stringify(config, null, 2) }}</pre>
      <pre v-if="machineInfo">{{ JSON.stringify(machineInfo, null, 2) }}</pre>
    </div>

    <div class="section">
      <h3>版本信息</h3>
      <button @click="getVersionInfo">获取版本信息</button>
      <p v-if="version">当前版本: {{ version }}</p>
      <pre v-if="versionInfo">{{ JSON.stringify(versionInfo, null, 2) }}</pre>
    </div>

    <div class="section">
      <h3>开发者工具</h3>
      <button @click="toggleDevTools">切换开发者工具</button>
    </div>

    <div class="section">
      <h3>全局变量管理</h3>
      <button @click="toggleTestMode">切换测试模式</button>
      <button @click="setClientId">设置客户端ID</button>
      <button @click="getClientId">获取客户端ID</button>
      <p v-if="clientId">客户端ID: {{ JSON.stringify(clientId) }}</p>
    </div>

    <div class="section">
      <h3>快捷键测试</h3>
      <button @click="executeShortcut">执行快捷键</button>
    </div>

    <div class="section" v-if="logs.length">
      <h3>操作日志</h3>
      <div class="logs">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import electronApi from '@/utils/electronApi'

export default {
  name: 'ElectronApiExample',
  data() {
    return {
      isElectron: electronApi.isElectron(),
      isTest: electronApi.globals.getIsTest(),
      config: null,
      machineInfo: null,
      version: null,
      versionInfo: null,
      clientId: null,
      logs: []
    }
  },
  methods: {
    addLog(message) {
      this.logs.unshift({
        time: new Date().toLocaleTimeString(),
        message
      })
      if (this.logs.length > 20) {
        this.logs = this.logs.slice(0, 20)
      }
    },

    async closeApp() {
      try {
        await electronApi.app.closeApp()
        this.addLog('关闭应用请求已发送')
      } catch (error) {
        this.addLog(`关闭应用失败: ${error.message}`)
      }
    },

    async restartApp() {
      try {
        await electronApi.app.restartApp()
        this.addLog('重启应用请求已发送')
      } catch (error) {
        this.addLog(`重启应用失败: ${error.message}`)
      }
    },

    async getConfig() {
      try {
        this.config = await electronApi.config.getConfig()
        this.addLog('配置获取成功')
      } catch (error) {
        this.addLog(`获取配置失败: ${error.message}`)
      }
    },

    async getMachineInfo() {
      try {
        this.machineInfo = await electronApi.device.getMachineInfo()
        this.addLog('机器信息获取成功')
      } catch (error) {
        this.addLog(`获取机器信息失败: ${error.message}`)
      }
    },

    async getVersionInfo() {
      try {
        const versionResult = await electronApi.version.getVersion()
        this.version = versionResult.version || versionResult
        
        const versionInfoResult = await electronApi.version.getVersionInfo()
        this.versionInfo = versionInfoResult.versionInfo || versionInfoResult
        
        this.addLog('版本信息获取成功')
      } catch (error) {
        this.addLog(`获取版本信息失败: ${error.message}`)
      }
    },

    async toggleDevTools() {
      try {
        await electronApi.dev.toggleDevTools()
        this.addLog('开发者工具切换完成')
      } catch (error) {
        this.addLog(`切换开发者工具失败: ${error.message}`)
      }
    },

    toggleTestMode() {
      this.isTest = !this.isTest
      electronApi.globals.setIsTest(this.isTest)
      this.addLog(`测试模式已${this.isTest ? '开启' : '关闭'}`)
    },

    setClientId() {
      const newClientId = {
        screen_num: 'DEMO_001',
        timestamp: Date.now()
      }
      electronApi.globals.setClientId(newClientId)
      this.addLog('客户端ID已设置')
    },

    getClientId() {
      this.clientId = electronApi.globals.getClientId()
      this.addLog('客户端ID已获取')
    },

    async executeShortcut() {
      try {
        await electronApi.shortcut.kuaijiejian({name: 'huanlianclose'})
        this.addLog('快捷键执行完成')
      } catch (error) {
        this.addLog(`快捷键执行失败: ${error.message}`)
      }
    }
  }
}
</script>

<style scoped>
.electron-api-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.section h3 {
  margin-top: 0;
  color: #333;
}

button {
  margin: 5px;
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #0056b3;
}

pre {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

.logs {
  max-height: 200px;
  overflow-y: auto;
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  margin-bottom: 5px;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 10px;
}

.log-message {
  color: #333;
}
</style>
