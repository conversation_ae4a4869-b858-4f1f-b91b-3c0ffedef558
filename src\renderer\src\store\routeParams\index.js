import { ref } from 'vue'
import { defineStore } from 'pinia'

// 路由传值数据
const routeParamsData = ref({})

// 设置路由传值数据
const setRouteParams = (data) => {
  routeParamsData.value = data
  console.log('设置路由传值数据:', data)
}

// 获取路由传值数据
const getRouteParams = () => {
  return routeParamsData.value
}

// 清空路由传值数据
const clearRouteParams = () => {
  routeParamsData.value = {}
  console.log('清空路由传值数据')
}

// 获取特定字段的路由传值数据
const getRouteParam = (key) => {
  return routeParamsData.value[key]
}

// 设置特定字段的路由传值数据
const setRouteParam = (key, value) => {
  routeParamsData.value[key] = value
  console.log(`设置路由传值数据 ${key}:`, value)
}

// 路由传值store
export const useRouteParamsStore = defineStore('routeParams', () => {
  return {
    routeParamsData,
    setRouteParams,
    getRouteParams,
    clearRouteParams,
    getRouteParam,
    setRouteParam
  }
})
